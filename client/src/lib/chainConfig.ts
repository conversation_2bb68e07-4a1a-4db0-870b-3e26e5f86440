import { getChainMetadata, getRpcUrlForChain } from "thirdweb/chains";
import type { Chain } from "thirdweb/chains";
import {
  client as thirdwebClient,
  ethereum,
  arbitrum,
  optimism,
  base,
  polygon,
  bsc,
  avalanche,
  fantom,
  sepolia,
  arbitrumSepolia,
  optimismSepolia,
  baseSepolia,
  polygonAmoy,
  bscTestnet,
} from "./thirdweb"; // Use existing client and chains

// Re-export client for convenience
export const client = thirdwebClient;

// Define supported chains using thirdweb's predefined chains
export const supportedChains: Chain[] = [
  // Ethereum and Layer 2s
  ethereum, // Ethereum Mainnet
  arbitrum, // Arbitrum One
  optimism, // Optimism
  base, // Base

  // Alternative Layer 1s
  polygon, // Polygon Mainnet
  bsc, // BNB Smart Chain
  avalanche, // Avalanche C-Chain
  fantom, // Fantom Opera

  // Testnets
  sepolia, // Sepolia Testnet
  arbitrumSepolia, // Arbitrum Sepolia
  optimismSepolia, // Optimism Sepolia
  baseSepolia, // Base Sepolia
  polygonAmoy, // Polygon Amoy Testnet
  bscTestnet, // BSC Testnet
];

// Legacy chain colors for UI consistency (can be removed later)
export const chainColors: Record<number, string> = {
  // Ethereum and Layer 2s
  1: "#627EEA", // Ethereum
  42161: "#28A0F0", // Arbitrum One
  10: "#FF0420", // Optimism
  8453: "#0052FF", // Base

  // Alternative Layer 1s
  137: "#8247E5", // Polygon
  56: "#F3BA2F", // BNB Smart Chain
  43114: "#E84142", // Avalanche C-Chain
  250: "#1969FF", // Fantom Opera

  // Testnets
  11155111: "#627EEA", // Sepolia
  421614: "#28A0F0", // Arbitrum Sepolia
  11155420: "#FF0420", // Optimism Sepolia
  84532: "#0052FF", // Base Sepolia
  80002: "#8247E5", // Polygon Amoy
  97: "#F3BA2F", // BSC Testnet
};

// Get chain by chain ID
export const getChainById = (chainId: number): Chain | undefined => {
  return supportedChains.find((chain) => chain.id === chainId);
};

// Get chain by name (async since we need to fetch metadata)
export const getChainByName = async (
  name: string
): Promise<Chain | undefined> => {
  for (const chain of supportedChains) {
    try {
      const metadata = await getChainMetadata(chain);
      if (metadata.name.toLowerCase() === name.toLowerCase()) {
        return chain;
      }
    } catch (error) {
      console.warn(`Failed to get metadata for chain ${chain.id}:`, error);
    }
  }
  return undefined;
};

// Block Explorer Configuration using thirdweb metadata
export interface BlockExplorer {
  name: string;
  url: string;
  apiUrl?: string;
}

// Get block explorers using thirdweb's chain metadata
export const getBlockExplorers = async (
  chainId: number
): Promise<BlockExplorer[]> => {
  const chain = getChainById(chainId);
  if (!chain) {
    return [{ name: "Etherscan", url: "https://etherscan.io" }];
  }

  try {
    const metadata = await getChainMetadata(chain);
    if (metadata.explorers && metadata.explorers.length > 0) {
      return metadata.explorers.map((explorer) => ({
        name: explorer.name,
        url: explorer.url,
        apiUrl: (explorer as any).apiUrl, // Type assertion since apiUrl might not be in the type
      }));
    }
  } catch (error) {
    console.warn(`Failed to get explorers for chain ${chainId}:`, error);
  }

  // Fallback to default
  return [{ name: "Etherscan", url: "https://etherscan.io" }];
};

// Generate explorer URLs for different types of data
export const getExplorerUrl = async (
  chainId: number,
  type: "address" | "tx" | "block" | "token",
  value: string
): Promise<string> => {
  const explorers = await getBlockExplorers(chainId);
  const baseUrl = explorers[0]?.url || "https://etherscan.io";

  switch (type) {
    case "address":
      return `${baseUrl}/address/${value}`;
    case "tx":
      return `${baseUrl}/tx/${value}`;
    case "block":
      return `${baseUrl}/block/${value}`;
    case "token":
      return `${baseUrl}/token/${value}`;
    default:
      return baseUrl;
  }
};

// Synchronous version for immediate use (with fallback URLs)
export const getExplorerUrlSync = (
  txHash: string,
  chainId?: number
): string => {
  const baseUrls: Record<number, string> = {
    1: "https://etherscan.io",
    42161: "https://arbiscan.io",
    10: "https://optimistic.etherscan.io",
    8453: "https://basescan.org",
    137: "https://polygonscan.com",
    56: "https://bscscan.com",
    43114: "https://snowtrace.io",
    250: "https://ftmscan.com",
    11155111: "https://sepolia.etherscan.io",
    421614: "https://sepolia.arbiscan.io",
    11155420: "https://sepolia-optimism.etherscan.io",
    84532: "https://sepolia.basescan.org",
    80002: "https://amoy.polygonscan.com",
    97: "https://testnet.bscscan.com",
  };

  const baseUrl = chainId
    ? baseUrls[chainId] || "https://etherscan.io"
    : "https://etherscan.io";
  return `${baseUrl}/tx/${txHash}`;
};

// Helper function to get chain metadata with caching
const chainMetadataCache = new Map<number, any>();

export const getCachedChainMetadata = async (chain: Chain) => {
  if (chainMetadataCache.has(chain.id)) {
    return chainMetadataCache.get(chain.id);
  }

  try {
    const metadata = await getChainMetadata(chain);
    chainMetadataCache.set(chain.id, metadata);
    return metadata;
  } catch (error) {
    console.warn(`Failed to get metadata for chain ${chain.id}:`, error);
    return null;
  }
};

// Helper function to get chain metadata by chainId (synchronous with fallback)
export const getCachedChainMetadataById = (chainId: number) => {
  // Check cache first
  if (chainMetadataCache.has(chainId)) {
    return chainMetadataCache.get(chainId);
  }

  // Get chain object
  const chain = getChainById(chainId);
  if (!chain) {
    console.warn(`Chain ${chainId} not found in supported chains`);
    return null;
  }

  // Return fallback metadata immediately (synchronous)
  const fallbackMetadata = {
    name: getChainName(chainId),
    nativeCurrency: {
      symbol: getChainSymbol(chainId),
      decimals: 18,
    },
    chainId: chainId,
  };

  // Async fetch real metadata in background
  getCachedChainMetadata(chain).then((metadata) => {
    if (metadata) {
      chainMetadataCache.set(chainId, metadata);
    }
  });

  return fallbackMetadata;
};

// Helper functions for fallback chain info
const getChainName = (chainId: number): string => {
  const names: Record<number, string> = {
    1: "Ethereum",
    42161: "Arbitrum One",
    10: "Optimism",
    8453: "Base",
    137: "Polygon",
    56: "BNB Smart Chain",
    43114: "Avalanche",
    250: "Fantom",
    11155111: "Sepolia",
    421614: "Arbitrum Sepolia",
    11155420: "Optimism Sepolia",
    84532: "Base Sepolia",
    80002: "Polygon Amoy",
    97: "BSC Testnet",
  };
  return names[chainId] || `Chain ${chainId}`;
};

const getChainSymbol = (chainId: number): string => {
  const symbols: Record<number, string> = {
    1: "ETH",
    42161: "ETH",
    10: "ETH",
    8453: "ETH",
    137: "MATIC",
    56: "BNB",
    43114: "AVAX",
    250: "FTM",
    11155111: "ETH",
    421614: "ETH",
    11155420: "ETH",
    84532: "ETH",
    80002: "POL",
    97: "BNB",
  };
  return symbols[chainId] || "ETH";
};

// Helper function to get RPC URL for a chain
export const getChainRpcUrl = (chain: Chain): string => {
  try {
    return getRpcUrlForChain({ chain, client });
  } catch (error) {
    console.warn(`Failed to get RPC URL for chain ${chain.id}:`, error);
    // Fallback RPC URLs
    const fallbackRpcs: Record<number, string> = {
      // Ethereum and Layer 2s
      1: "https://ethereum.rpc.thirdweb.com",
      42161: "https://arbitrum.rpc.thirdweb.com",
      10: "https://optimism.rpc.thirdweb.com",
      8453: "https://base.rpc.thirdweb.com",

      // Alternative Layer 1s
      137: "https://polygon.rpc.thirdweb.com",
      56: "https://binance.rpc.thirdweb.com",
      43114: "https://avalanche.rpc.thirdweb.com",
      250: "https://fantom.rpc.thirdweb.com",

      // Testnets
      11155111: "https://sepolia.rpc.thirdweb.com",
      421614: "https://arbitrum-sepolia.rpc.thirdweb.com",
      11155420: "https://optimism-sepolia.rpc.thirdweb.com",
      84532: "https://base-sepolia.rpc.thirdweb.com",
      80002: "https://amoy.rpc.thirdweb.com",
      97: "https://bsc-testnet.rpc.thirdweb.com",
    };
    return fallbackRpcs[chain.id] || "https://ethereum.rpc.thirdweb.com";
  }
};

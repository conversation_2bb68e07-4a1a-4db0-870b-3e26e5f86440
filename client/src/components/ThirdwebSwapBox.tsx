import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { ArrowUpDown, ExternalLink, CheckCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useActiveAccount, PayEmbed, useWaitForReceipt } from "thirdweb/react";
import {
  prepareTransaction,
  define<PERSON>hain,
  getContract,
  prepareContractCall,
} from "thirdweb";
import { client } from "@/lib/thirdweb";
import {
  getExplorerUrl,
  getCachedChainMetadata,
  formatChainName,
} from "@/lib/chainConfig";
import type { BridgePrepareResponse } from "@/lib/bridgeApi";
import SmartAccountTransactionButton from "./SmartAccountTransactionButton";

interface SwapData {
  fromToken: {
    symbol: string;
    address: string;
    amount: string;
    decimals: number;
  };
  toToken: {
    symbol: string;
    address: string;
    amount: string;
    decimals: number;
  };
  chainId: number;
  estimatedGas?: string;
  slippage?: string;
}

interface ThirdwebSwapBoxProps {
  title: string;
  swapData: SwapData;
  preparedQuote?: BridgePrepareResponse["preparedQuote"];
  onExecute?: (swapData: any) => Promise<string | undefined>;
  transactionHash?: string | null;
  className?: string;
  usePayEmbed?: boolean; // Option to use PayEmbed vs custom implementation
}

const ThirdwebSwapBox = ({
  title,
  swapData,
  preparedQuote,
  onExecute,
  transactionHash,
  className = "",
  usePayEmbed = false,
}: ThirdwebSwapBoxProps) => {
  const { toast } = useToast();
  const activeAccount = useActiveAccount();
  const [currentTxHash, setCurrentTxHash] = useState<string | null>(
    transactionHash
  );
  const [currentStep, setCurrentStep] = useState<"approval" | "swap">(
    "approval"
  );

  // Get chain metadata for display
  const chainMetadata = getCachedChainMetadata(swapData.chainId);
  const chainName = chainMetadata?.name || `Chain ${swapData.chainId}`;
  const displayChainName = formatChainName(chainName);

  // Wait for receipt if we have a transaction hash
  const receiptQuery = useWaitForReceipt({
    client,
    chain: defineChain(swapData.chainId),
    transactionHash: currentTxHash as `0x${string}`,
  });

  // Prepare transactions from the quote
  const preparedTransactions = useMemo(() => {
    if (!preparedQuote?.steps) return [];

    const transactions = [];
    for (const step of preparedQuote.steps) {
      for (const tx of step.transactions) {
        try {
          const chain = defineChain(swapData.chainId);
          const preparedTx = prepareTransaction({
            to: tx.to as `0x${string}`,
            value: BigInt(tx.value || "0"),
            data: tx.data as `0x${string}`,
            gas: tx.gasLimit ? BigInt(tx.gasLimit) : undefined,
            chain,
            client,
          });

          transactions.push({
            ...preparedTx,
            action: tx.action,
            stepIndex: step,
          });
        } catch (error) {
          console.error("Failed to prepare transaction:", error);
        }
      }
    }
    return transactions;
  }, [preparedQuote, swapData.chainId]);

  const handleTransactionSent = (result: { transactionHash: string }) => {
    setCurrentTxHash(result.transactionHash);
    toast({
      title: "Transaction Submitted",
      description: `Transaction hash: ${result.transactionHash}`,
    });
  };

  const handleTransactionConfirmed = () => {
    toast({
      title: "Transaction Confirmed",
      description: "Your transaction has been confirmed on the blockchain",
    });
  };

  const handleError = (error: Error) => {
    console.error("Transaction failed:", error);
    toast({
      title: "Transaction Failed",
      description: error.message || "Failed to execute transaction",
      variant: "destructive",
    });
  };

  const handleViewOnExplorer = () => {
    if (currentTxHash) {
      const explorerUrl = getExplorerUrl(currentTxHash, swapData.chainId);
      window.open(explorerUrl, "_blank");
    }
  };

  // If using PayEmbed, render the thirdweb PayEmbed component
  if (usePayEmbed) {
    return (
      <div className={`nebula-transaction-card ${className}`}>
        <div className="text-sm font-medium mb-3">{title}</div>
        <PayEmbed
          client={client}
          theme="dark"
          payOptions={{
            mode: "fund_wallet",
            metadata: {
              name: title,
            },
            prefillBuy: {
              chain: defineChain(swapData.chainId),
              amount: swapData.fromToken.amount,
            },
          }}
        />
      </div>
    );
  }

  return (
    <div className={`nebula-transaction-card max-w-lg w-full ${className}`}>
      {/* Header */}
      <div className="text-sm font-medium mb-3 flex items-center justify-between">
        <span>{title}</span>
        {receiptQuery.data && (
          <CheckCircle className="h-4 w-4 text-green-500" />
        )}
      </div>

      {/* Swap Details - Nebula Style */}
      <div className="space-y-0 mb-4">
        {/* Sell Section */}
        <div className="flex items-center justify-between py-3 border-b border-border/30">
          <div>
            <div className="text-sm text-muted-foreground mb-1">Sell</div>
            <div className="font-medium text-base">
              {swapData.fromToken.amount} {swapData.fromToken.symbol}
            </div>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
            <span>{displayChainName}</span>
          </div>
        </div>

        {/* Buy Section */}
        <div className="flex items-center justify-between py-3">
          <div>
            <div className="text-sm text-muted-foreground mb-1">Buy</div>
            <div className="font-medium text-base">
              {swapData.toToken.amount} {swapData.toToken.symbol}
            </div>
          </div>
          <div className="flex items-center text-sm text-muted-foreground">
            <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
            <span>{displayChainName}</span>
          </div>
        </div>
      </div>

      {/* Transaction Steps */}
      {preparedQuote && preparedQuote.steps.length > 0 && (
        <div className="mb-4">
          <div className="text-xs text-muted-foreground mb-2">
            Transaction Steps:
          </div>
          <div className="space-y-1">
            {preparedQuote.steps.map((step, stepIndex) =>
              step.transactions.map((tx, txIndex) => (
                <div
                  key={`${stepIndex}-${txIndex}`}
                  className="text-xs bg-muted/20 p-2 rounded flex items-center justify-between"
                >
                  <div>
                    <div className="font-medium">
                      {tx.action === "approval" ? "Approve" : "Swap"}{" "}
                      {step.originToken.symbol}
                    </div>
                    <div className="text-muted-foreground truncate">
                      To: {tx.to}
                    </div>
                  </div>
                  {tx.action === "approval" && (
                    <div className="text-xs px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded">
                      Approval Required
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Status */}
      {currentTxHash && (
        <div className="mb-4 p-3 bg-muted/20 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status</span>
            <div className="flex items-center">
              {receiptQuery.isLoading ? (
                <>
                  <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-2" />
                  <span className="text-yellow-500 text-sm">Pending</span>
                </>
              ) : receiptQuery.data ? (
                <>
                  <CheckCircle className="h-3 w-3 text-green-500 mr-2" />
                  <span className="text-green-500 text-sm">Confirmed</span>
                </>
              ) : (
                <span className="text-red-500 text-sm">Failed</span>
              )}
            </div>
          </div>
          {currentTxHash && (
            <div className="mt-2">
              <button
                onClick={handleViewOnExplorer}
                className="text-blue-400 hover:text-blue-300 underline text-xs"
              >
                View Transaction ↗
              </button>
            </div>
          )}
        </div>
      )}

      {/* Action Button */}
      <div className="pt-3 border-t border-border/50">
        {currentTxHash ? (
          <div className="space-y-2">
            <Button
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              disabled
            >
              ✓ Swap Executed
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={handleViewOnExplorer}
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              View on Explorer
            </Button>
          </div>
        ) : preparedTransactions.length > 0 ? (
          <SmartAccountTransactionButton
            transaction={preparedTransactions[0]}
            onTransactionSent={handleTransactionSent}
            onTransactionConfirmed={handleTransactionConfirmed}
            onError={handleError}
            className="w-full nebula-action-button text-primary-foreground"
          >
            ⇄ Swap Tokens
          </SmartAccountTransactionButton>
        ) : (
          <Button
            className="w-full nebula-action-button text-primary-foreground"
            disabled
          >
            ⇄ Swap Tokens
          </Button>
        )}
      </div>
    </div>
  );
};

export default ThirdwebSwapBox;
